"""
OpenAI API 密钥配置脚本
帮助用户安全地设置 OpenAI API 密钥
"""

import os
import getpass

def setup_openai_api():
    print("🔧 OpenAI API 密钥配置")
    print("=" * 50)
    
    # 检查是否已经设置了 API 密钥
    # 设置 API 密钥
    api_key = "********************************************************************************************************************************************************************"
    os.environ["OPENAI_API_KEY"] = api_key
    current_key = os.getenv("OPENAI_API_KEY")
    if current_key:
        print(f"✅ 检测到已设置的 API 密钥: {current_key[:10]}...")
        choice = input("是否要更新 API 密钥? (y/n): ").lower()
        if choice != 'y':
            print("保持当前设置")
            return True
    
    print("\n📋 获取 OpenAI API 密钥的步骤:")
    print("1. 访问 https://platform.openai.com/api-keys")
    print("2. 登录您的 OpenAI 账户")
    print("3. 点击 'Create new secret key'")
    print("4. 复制生成的 API 密钥 (以 sk- 开头)")
    print()
    
    # 安全地输入 API 密钥
    api_key = getpass.getpass("请输入您的 OpenAI API 密钥 (输入时不会显示): ").strip()
    
    if not api_key:
        print("❌ API 密钥不能为空")
        return False
    
    if not api_key.startswith("sk-"):
        print("⚠️  警告: OpenAI API 密钥通常以 'sk-' 开头")
        choice = input("是否继续? (y/n): ").lower()
        if choice != 'y':
            return False
    
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = api_key
    print("✅ API 密钥已设置到当前会话")
    
    # 测试 API 密钥
    print("\n🧪 测试 API 密钥...")
    try:
        import openai
        
        # 创建客户端
        client = openai.OpenAI(api_key=api_key)
        
        # 简单测试
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print("✅ API 密钥验证成功!")
        print(f"测试响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API 密钥验证失败: {e}")
        return False

def show_permanent_setup_instructions():
    print("\n" + "=" * 50)
    print("🔒 永久设置 API 密钥 (可选)")
    print("=" * 50)
    print("如果您想永久设置 API 密钥，可以选择以下方法之一:")
    print()
    print("方法 1: 系统环境变量 (推荐)")
    print("1. 按 Win + R，输入 'sysdm.cpl'")
    print("2. 点击 '环境变量' 按钮")
    print("3. 在 '用户变量' 中点击 '新建'")
    print("4. 变量名: OPENAI_API_KEY")
    print("5. 变量值: 您的 API 密钥")
    print("6. 重启 PowerShell/命令提示符")
    print()
    print("方法 2: .env 文件")
    print("1. 在项目根目录创建 .env 文件")
    print("2. 添加一行: OPENAI_API_KEY=sk-your_api_key_here")
    print("3. 确保 .env 文件不被提交到版本控制")

def main():
    success = setup_openai_api()
    
    if success:
        print("\n🎉 配置完成!")
        print("现在您可以运行验证脚本:")
        print("  python test_openai_pandasai.py")
        
        show_permanent_setup_instructions()
    else:
        print("\n❌ 配置失败，请检查您的 API 密钥")
    
    return success

if __name__ == "__main__":
    main()
