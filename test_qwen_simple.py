"""
阿里通义千问 + PandasAI V3 简化测试脚本
验证通义千问模型是否正常工作
"""

import os
import pandas as pd
import pandasai as pai

def test_qwen_simple():
    """简化的通义千问测试"""
    print("🚀 阿里通义千问 + PandasAI V3 简化测试")
    print("=" * 50)
    
    # 检查 API 密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY 环境变量")
        print("请先运行: python setup_qwen_dashscope.py")
        return False
    
    print(f"✅ API 密钥已设置: {api_key[:10]}...")
    
    try:
        # 导入和配置
        from pandasai_litellm import LiteLLM
        print("✅ LiteLLM 导入成功")
        
        # 配置通义千问模型
        llm = LiteLLM(
            model="dashscope/qwen-plus",
            temperature=0.1,
            max_tokens=1000
        )
        print("✅ 通义千问 qwen-plus 模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({"llm": llm})
        print("✅ PandasAI 配置完成")
        
        # 创建简单的中文测试数据
        df = pai.DataFrame({
            "产品": ["iPhone", "华为P60", "小米13", "OPPO Find X6"],
            "价格": [6999, 4988, 3999, 5999],
            "销量": [1200, 800, 1500, 600],
            "评分": [4.5, 4.3, 4.2, 4.1]
        })
        print("✅ 测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 执行简单查询
        print("\n🔍 执行测试查询: 哪款产品性价比最高？")
        try:
            result = df.chat("哪款产品性价比最高？请分析价格和评分的关系")
            print(f"✅ 查询成功!")
            print(f"通义千问回答: {result}")
            
            print("\n🎉 通义千问 + PandasAI V3 配置成功!")
            return True
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False

def main():
    print("🌟 阿里通义千问配置验证")
    print("=" * 40)
    
    success = test_qwen_simple()
    
    if success:
        print("\n📚 配置成功! 您现在可以:")
        print("1. 使用中文进行数据分析")
        print("2. 享受更低的API成本")
        print("3. 获得更好的中文理解能力")
        print("\n💡 使用示例:")
        print("df.chat('分析销售数据的趋势')")
        print("df.chat('创建一个柱状图显示各产品销量')")
        print("df.chat('计算平均价格和总销量')")
    else:
        print("\n❌ 配置失败，请:")
        print("1. 检查 API 密钥是否正确")
        print("2. 确认网络连接正常")
        print("3. 重新运行 setup_qwen_dashscope.py")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
