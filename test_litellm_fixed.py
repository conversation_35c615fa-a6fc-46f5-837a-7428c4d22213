"""
修正的 LiteLLM + PandasAI V3 集成测试
基于 LiteLLM 官方文档的正确配置方式
"""

import os
import sys
from pathlib import Path

def load_env_file():
    """从 .env 文件加载环境变量"""
    env_file = Path('.env')
    if not env_file.exists():
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        return True
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return False

def test_litellm_direct():
    """直接测试 LiteLLM 与 Dashscope 的连接"""
    print("🔧 测试 LiteLLM 直接连接")
    print("=" * 40)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API 密钥已加载: {api_key[:10]}...")
    
    try:
        # 导入 LiteLLM
        from litellm import completion
        print("✅ LiteLLM 导入成功")
        
        # 设置环境变量（LiteLLM 要求）
        os.environ['DASHSCOPE_API_KEY'] = api_key
        
        # 测试 1: 基本连接
        print("\n🔍 测试 1: LiteLLM 基本连接")
        response = completion(
            model="dashscope/qwen-plus",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            max_tokens=100
        )
        
        print("✅ LiteLLM 连接成功!")
        print(f"回答: {response.choices[0].message.content}")
        
        # 测试 2: 流式输出
        print("\n🌊 测试 2: LiteLLM 流式输出")
        response_stream = completion(
            model="dashscope/qwen-plus",
            messages=[
                {"role": "user", "content": "请简单解释什么是数据分析"}
            ],
            stream=True,
            max_tokens=80
        )
        
        print("✅ 流式输出开始:")
        print("回答: ", end="", flush=True)
        for chunk in response_stream:
            if chunk.choices[0].delta.content is not None:
                print(chunk.choices[0].delta.content, end="", flush=True)
        print()  # 换行
        print("✅ 流式输出完成")
        
        return True
        
    except Exception as e:
        print(f"❌ LiteLLM 测试失败: {e}")
        return False

def test_pandasai_litellm_integration():
    """测试 PandasAI + LiteLLM 集成"""
    print("\n🚀 PandasAI + LiteLLM 集成测试")
    print("=" * 50)
    
    try:
        # 导入必要的库
        import pandasai as pai
        from pandasai_litellm import LiteLLM
        print("✅ PandasAI 和 LiteLLM 扩展导入成功")
        
        # 创建 LiteLLM 实例 - 使用正确的配置方式
        llm = LiteLLM(
            model="dashscope/qwen-plus",
            temperature=0.1,
            max_tokens=1500
        )
        print("✅ LiteLLM 实例创建成功")
        
        # 设置 PandasAI 配置
        pai.config.set({
            "llm": llm,
            "save_charts": True,
            "save_charts_path": "./charts/",
            "verbose": True
        })
        print("✅ PandasAI 配置完成")
        
        # 创建测试数据
        df = pai.DataFrame({
            "城市": ["北京", "上海", "广州", "深圳"],
            "GDP": [40000, 43000, 28000, 32000],
            "人口": [2154, 2489, 1868, 1756],
            "评分": [8.5, 9.0, 8.2, 8.8]
        })
        print("✅ 测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试查询
        print("\n🔍 执行测试查询: 哪个城市GDP最高？")
        result = df.chat("哪个城市的GDP最高？")
        print(f"✅ 查询成功!")
        print(f"通义千问回答: {result}")
        
        # 测试计算
        print("\n📊 执行计算测试: 计算平均GDP")
        result2 = df.chat("计算所有城市的平均GDP是多少？")
        print(f"✅ 计算成功!")
        print(f"通义千问回答: {result2}")
        
        return True
        
    except Exception as e:
        print(f"❌ PandasAI + LiteLLM 集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_fixed_usage_example():
    """创建修正后的使用示例"""
    example_code = '''
# PandasAI V3 + LiteLLM + 通义千问 正确配置示例

import os
import pandasai as pai
from pandasai_litellm import LiteLLM

# 1. 设置环境变量
os.environ["DASHSCOPE_API_KEY"] = "your-api-key"

# 2. 创建 LiteLLM 实例 - 关键：使用 dashscope/ 前缀
llm = LiteLLM(
    model="dashscope/qwen-plus",  # 必须使用 dashscope/ 前缀
    temperature=0.1,
    max_tokens=2000
)

# 3. 配置 PandasAI
pai.config.set({
    "llm": llm,
    "save_charts": True,
    "save_charts_path": "./charts/"
})

# 4. 创建数据并开始分析
df = pai.DataFrame({
    "产品": ["iPhone", "华为", "小米"],
    "销量": [1000, 800, 1200],
    "价格": [6999, 4999, 3999]
})

# 5. 使用中文进行数据分析
result = df.chat("哪个产品性价比最高？")
print(result)

# 6. 生成图表
chart = df.chat("创建销量对比柱状图")
print(chart)
'''
    
    with open("litellm_fixed_example.py", "w", encoding="utf-8") as f:
        f.write(example_code)
    
    print("📝 修正后的使用示例已保存到 litellm_fixed_example.py")

def main():
    """主函数"""
    print("🔧 LiteLLM 配置修正验证")
    print("=" * 60)
    print("基于 LiteLLM 官方文档的正确配置方式")
    
    # 加载环境变量
    if not load_env_file():
        print("❌ 环境变量加载失败")
        return False
    
    # 测试 LiteLLM 直接连接
    litellm_success = test_litellm_direct()
    
    # 如果 LiteLLM 直接连接成功，测试 PandasAI 集成
    pandasai_success = False
    if litellm_success:
        pandasai_success = test_pandasai_litellm_integration()
    
    # 创建修正后的示例
    if litellm_success:
        create_fixed_usage_example()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 修正验证结果:")
    print(f"  LiteLLM 直接连接: {'✅ 成功' if litellm_success else '❌ 失败'}")
    print(f"  PandasAI + LiteLLM: {'✅ 成功' if pandasai_success else '❌ 失败'}")
    
    if litellm_success and pandasai_success:
        print("\n🎉 LiteLLM 配置修正成功!")
        print("\n📚 关键修正点:")
        print("1. ✅ 使用 'dashscope/qwen-plus' 而不是 'qwen-plus'")
        print("2. ✅ 确保 DASHSCOPE_API_KEY 环境变量正确设置")
        print("3. ✅ 使用正确的 LiteLLM 导入和配置方式")
        print("\n💡 现在您可以:")
        print("- 使用标准的 LiteLLM 配置")
        print("- 享受 PandasAI 的完整功能")
        print("- 获得通义千问的中文优势")
    elif litellm_success:
        print("\n✅ LiteLLM 直接连接成功!")
        print("❌ PandasAI 集成仍有问题，但基础连接正常")
    else:
        print("\n❌ LiteLLM 连接失败")
        print("🔧 请检查:")
        print("1. API 密钥是否正确")
        print("2. 网络连接是否正常")
        print("3. LiteLLM 版本是否最新")
    
    return litellm_success and pandasai_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
