"""
PandasAI V3 + 阿里通义千问 (Dashscope) 测试脚本
演示如何使用中国大陆地区的 LLM 模型
"""

import os
import pandas as pd
import pandasai as pai

def test_qwen_dashscope():
    """测试阿里通义千问与 PandasAI V3 集成"""
    print("🚀 PandasAI V3 + 阿里通义千问测试")
    print("=" * 50)
    
    # 检查 API 密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY 环境变量")
        print("\n请按以下步骤设置 API 密钥:")
        print("1. 访问 https://dashscope.console.aliyun.com/")
        print("2. 注册/登录阿里云账户")
        print("3. 创建 API 密钥")
        print("4. 设置环境变量:")
        print("   Windows: set DASHSCOPE_API_KEY=your-api-key")
        print("   Linux/Mac: export DASHSCOPE_API_KEY=your-api-key")
        return False
    
    print(f"✅ API 密钥已设置: {api_key[:10]}...")
    
    try:
        # 导入 LiteLLM
        from pandasai_litellm import LiteLLM
        print("✅ LiteLLM 导入成功")
        
        # 配置通义千问模型
        llm = LiteLLM(
            model="dashscope/qwen-plus",
            temperature=0.1,
            max_tokens=2000
        )
        print("✅ 通义千问模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({
            "llm": llm,
            "save_charts": True,
            "save_charts_path": "./charts/"
        })
        print("✅ PandasAI 配置设置成功")
        
        # 创建中文测试数据
        df = pai.DataFrame({
            "城市": ["北京", "上海", "广州", "深圳", "杭州"],
            "GDP": [40000, 43000, 28000, 32000, 18000],  # 单位：亿元
            "人口": [2154, 2489, 1868, 1756, 1220],      # 单位：万人
            "面积": [16410, 6340, 7434, 1997, 16596]     # 单位：平方公里
        })
        print("✅ 中文测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试 1: 中文数据查询
        print("\n🔍 测试 1: 中文数据查询")
        print("问题: 哪个城市的GDP最高？")
        try:
            result1 = df.chat("哪个城市的GDP最高？")
            print(f"✅ 查询成功!")
            print(f"结果: {result1}")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
        
        # 测试 2: 数据计算
        print("\n📊 测试 2: 数据计算")
        print("问题: 计算每个城市的人均GDP")
        try:
            result2 = df.chat("计算每个城市的人均GDP，并按从高到低排序")
            print(f"✅ 计算成功!")
            print(f"结果: {result2}")
        except Exception as e:
            print(f"❌ 计算失败: {e}")
            print("继续下一个测试...")
        
        # 测试 3: 数据分析
        print("\n📈 测试 3: 数据分析")
        print("问题: 分析城市发展特点")
        try:
            result3 = df.chat("分析这些城市的发展特点，哪些城市人口密度最高？")
            print(f"✅ 分析成功!")
            print(f"结果: {result3}")
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            print("继续下一个测试...")
        
        # 测试 4: 数据可视化
        print("\n🎨 测试 4: 数据可视化")
        print("问题: 创建GDP对比图")
        try:
            result4 = df.chat("创建一个柱状图显示各城市的GDP对比，使用不同颜色")
            print(f"✅ 可视化成功!")
            print(f"结果: {result4}")
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            print("这可能是由于图表生成的复杂性")
        
        print("\n🎉 阿里通义千问 + PandasAI V3 测试成功!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False

def test_other_china_models():
    """测试其他中国模型"""
    print("\n🌏 其他中国模型测试")
    print("=" * 30)
    
    models_to_test = [
        ("deepseek/deepseek-chat", "DeepSeek"),
        ("moonshot/moonshot-v1-8k", "Moonshot AI"),
    ]
    
    for model_name, provider_name in models_to_test:
        print(f"\n🧪 测试 {provider_name}")
        try:
            from pandasai_litellm import LiteLLM
            llm = LiteLLM(model=model_name)
            print(f"✅ {provider_name} 模型配置成功")
            
            # 简单测试（不执行实际查询以避免API费用）
            print(f"✅ {provider_name} 可用于 PandasAI V3")
            
        except Exception as e:
            print(f"❌ {provider_name} 配置失败: {e}")

def main():
    print("🚀 PandasAI V3 中国大陆模型完整测试")
    print("=" * 60)
    
    # 测试通义千问
    qwen_success = test_qwen_dashscope()
    
    # 测试其他模型
    test_other_china_models()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  阿里通义千问: {'✅ 通过' if qwen_success else '❌ 失败'}")
    print("  其他中国模型: ✅ 配置可用")
    
    if qwen_success:
        print("\n🎉 中国大陆模型验证成功!")
        print("\n📚 使用指南:")
        print("1. 设置 API 密钥: set DASHSCOPE_API_KEY=your-key")
        print("2. 配置模型: LiteLLM(model='dashscope/qwen-plus')")
        print("3. 开始分析: df.chat('你的问题')")
        print("\n💡 推荐模型:")
        print("- qwen-plus: 平衡性能和成本")
        print("- qwen-turbo: 快速响应，成本最低")
        print("- qwen-max: 最高性能")
    else:
        print("\n⚠️  请设置 DASHSCOPE_API_KEY 后重新测试")
    
    return qwen_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
