"""
PandasAI V3 + OpenAI 验证测试
使用 LiteLLM 扩展和 OpenAI GPT 模型
"""

import os
import pandas as pd
import pandasai as pai

def test_openai_connection():
    """测试 OpenAI API 连接"""
    print("🧪 测试 OpenAI API 连接")
    print("-" * 30)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 未找到 OPENAI_API_KEY 环境变量")
        return False
    
    print(f"✅ API 密钥已设置: {api_key[:10]}...")
    
    try:
        import openai
        
        # 创建客户端
        client = openai.OpenAI(api_key=api_key)
        
        # 简单测试
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello, respond with 'API test successful'"}],
            max_tokens=10
        )
        
        print("✅ OpenAI API 连接成功!")
        print(f"测试响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API 连接失败: {e}")
        return False

def test_pandasai_v3_with_openai():
    """测试 PandasAI V3 与 OpenAI 集成"""
    print("\n🚀 PandasAI V3 + OpenAI 测试")
    print("=" * 50)
    
    try:
        # 导入 LiteLLM
        from pandasai_litellm import LiteLLM
        print("✅ LiteLLM 导入成功")
        
        # 配置 OpenAI 模型
        llm = LiteLLM(model="gpt-3.5-turbo")
        print("✅ OpenAI 模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({"llm": llm})
        print("✅ PandasAI 配置设置成功")
        
        # 创建测试数据
        df = pai.DataFrame({
            "country": ["USA", "UK", "France", "Germany", "Japan"],
            "gdp": [21400000, 2940000, 2830000, 3860000, 5150000],
            "happiness": [7.3, 7.2, 6.5, 7.0, 5.9],
            "population": [331000000, 67000000, 67000000, 83000000, 125000000]
        })
        print("✅ 测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试 1: 简单查询
        print("\n🔍 测试 1: 数据查询")
        print("问题: 哪个国家最幸福？")
        try:
            result1 = df.chat("Which country has the highest happiness score?")
            print(f"✅ 查询成功!")
            print(f"结果: {result1}")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
        
        # 测试 2: 数据分析
        print("\n📊 测试 2: 数据分析")
        print("问题: 平均 GDP 是多少？")
        try:
            result2 = df.chat("What is the average GDP of all countries?")
            print(f"✅ 分析成功!")
            print(f"结果: {result2}")
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            print("继续下一个测试...")
        
        # 测试 3: 数据比较
        print("\n📈 测试 3: 数据比较")
        print("问题: GDP 最高的前3个国家是哪些？")
        try:
            result3 = df.chat("What are the top 3 countries with the highest GDP?")
            print(f"✅ 比较成功!")
            print(f"结果: {result3}")
        except Exception as e:
            print(f"❌ 比较失败: {e}")
            print("继续下一个测试...")
        
        # 测试 4: 数据可视化
        print("\n🎨 测试 4: 数据可视化")
        print("问题: 创建 GDP 柱状图")
        try:
            result4 = df.chat("Create a bar chart showing GDP by country")
            print(f"✅ 可视化成功!")
            print(f"结果: {result4}")
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            print("这可能是由于图表生成的复杂性")
        
        print("\n🎉 PandasAI V3 + OpenAI 基本功能验证成功!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False

def main():
    print("🚀 PandasAI V3 + OpenAI 完整验证")
    print("=" * 60)
    
    # 检查 API 密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 未找到 OPENAI_API_KEY 环境变量")
        print("请先运行: python setup_openai_api.py")
        return False
    
    # 测试 OpenAI 连接
    openai_ok = test_openai_connection()
    
    if not openai_ok:
        print("\n❌ OpenAI API 测试失败，无法继续")
        return False
    
    # 测试 PandasAI V3 集成
    pandasai_ok = test_pandasai_v3_with_openai()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  OpenAI API 连接: {'✅ 通过' if openai_ok else '❌ 失败'}")
    print(f"  PandasAI V3 集成: {'✅ 通过' if pandasai_ok else '❌ 失败'}")
    
    if openai_ok and pandasai_ok:
        print("\n🎉 所有测试通过! PandasAI V3 + OpenAI 配置成功!")
        print("\n📚 您现在可以使用以下方式进行数据分析:")
        print("1. 创建 DataFrame: df = pai.DataFrame(your_data)")
        print("2. 配置 LLM: pai.config.set({'llm': LiteLLM(model='gpt-3.5-turbo')})")
        print("3. 开始对话: df.chat('your question')")
        print("\n💡 提示: 您可以使用 gpt-4 获得更好的结果 (费用更高)")
    else:
        print("\n❌ 测试失败，请检查配置")
    
    return openai_ok and pandasai_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
