# PandasAI V3 功能验证方案

这是一个基于 PandasAI V3 官方 GitHub 文档的最简单功能验证方案，可以直接在 `augmentcode` 环境中使用。

## 📋 方案概述

本方案将验证 PandasAI V3 的核心功能：
1. **数据查询** - 使用自然语言查询数据
2. **数据分析** - 执行统计分析
3. **数据可视化** - 生成图表

## 🚀 快速开始

### 步骤 1: 安装依赖

运行自动安装脚本：
```bash
python install_dependencies.py
```

或者手动安装：
```bash
pip install pandas pandasai openai google-generativeai matplotlib seaborn
```

### 步骤 2: 配置 API 密钥

选择以下任一 LLM 提供商并设置相应的环境变量：

#### 选项 A: OpenAI (推荐)
```bash
# Windows (PowerShell)
$env:OPENAI_API_KEY="sk-your-openai-api-key"

# Windows (CMD)
set OPENAI_API_KEY=sk-your-openai-api-key

# Linux/Mac
export OPENAI_API_KEY="sk-your-openai-api-key"
```

#### 选项 B: Google Gemini (免费额度)
```bash
# Windows (PowerShell)
$env:GOOGLE_API_KEY="your-google-api-key"

# Windows (CMD)
set GOOGLE_API_KEY=your-google-api-key

# Linux/Mac
export GOOGLE_API_KEY="your-google-api-key"
```

### 步骤 3: 运行验证

```bash
python pandasai_verification.py
```

## 📊 示例数据

验证脚本使用以下示例数据集：

| Country | GDP | Happiness Index | Population |
|---------|-----|-----------------|------------|
| United States | 21,400,000 | 7.3 | 331,000,000 |
| United Kingdom | 2,940,000 | 7.2 | 67,000,000 |
| France | 2,830,000 | 6.5 | 67,000,000 |
| ... | ... | ... | ... |

## 🧪 测试内容

### 测试 1: 数据查询
- **问题**: "Which are the 5 happiest countries?"
- **预期**: 返回按幸福指数排序的前5个国家

### 测试 2: 数据分析
- **问题**: "What is the correlation between GDP and happiness index?"
- **预期**: 计算并返回GDP与幸福指数的相关性

### 测试 3: 数据可视化
- **问题**: "Plot a bar chart of GDP by country, using different colors for each bar"
- **预期**: 生成GDP柱状图并保存为文件

## 📈 预期输出

成功运行后，您应该看到类似以下的输出：

```
🚀 PandasAI V3 功能验证开始
==================================================
📊 创建示例数据集...
✅ 数据集创建完成，包含 10 个国家的数据

🤖 初始化 LLM...
✅ 成功连接到 OpenAI

🧠 创建 SmartDataframe...
✅ SmartDataframe 创建成功

🔍 测试 1: 数据查询
问题: 哪5个国家最幸福？
✅ 查询成功!

📈 测试 2: 数据分析
问题: GDP和幸福指数之间的相关性是什么？
✅ 分析成功!

📊 测试 3: 数据可视化
问题: 绘制GDP的直方图，每个柱子使用不同颜色
✅ 可视化成功!

==================================================
📋 验证结果总结:
  1. 数据查询: ✅ 通过
  2. 数据分析: ✅ 通过
  3. 数据可视化: ✅ 通过

总体结果: 3/3 测试通过
🎉 所有测试通过! PandasAI V3 功能验证成功!
```

## 🔧 故障排除

### 常见问题

1. **API 密钥错误**
   - 确保 API 密钥正确设置
   - 检查密钥是否有效且有足够额度

2. **网络连接问题**
   - 确保网络连接正常
   - 检查是否需要代理设置

3. **依赖包安装失败**
   - 尝试升级 pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 调试模式

如果遇到问题，可以在脚本中添加调试信息：

```python
# 在 pandasai_verification.py 中添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 进一步学习

- [PandasAI 官方文档](https://docs.pandas-ai.com/)
- [PandasAI GitHub 仓库](https://github.com/gventuri/pandas-ai)
- [OpenAI API 文档](https://platform.openai.com/docs)
- [Google AI Studio](https://makersuite.google.com/)

## 🤝 支持

如果您在使用过程中遇到问题，请：
1. 检查上述故障排除部分
2. 查看 PandasAI 官方文档
3. 在相关 GitHub 仓库提交 issue

---

**注意**: 此验证方案基于 PandasAI V3 的最新版本。如果您使用的是其他版本，可能需要调整代码。
