"""
PandasAI V3 + Google Gemini 正确配置测试
使用 LiteLLM 扩展和新的 V3 API
"""

import os
import pandas as pd
import pandasai as pai

def test_pandasai_v3_with_gemini():
    print("🚀 PandasAI V3 + Google Gemini 测试")
    print("=" * 50)
    
    # 设置 API 密钥
    api_key = "AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50"
    os.environ["GOOGLE_API_KEY"] = api_key
    
    print(f"✅ Google API 密钥已设置: {api_key[:10]}...")
    
    try:
        # 导入 LiteLLM
        from pandasai_litellm import LiteLLM
        print("✅ LiteLLM 导入成功")
        
        # 配置 Gemini 模型
        llm = LiteLLM(model="gemini/gemini-pro")
        print("✅ Gemini 模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({"llm": llm})
        print("✅ PandasAI 配置设置成功")
        
        # 创建测试数据
        df = pai.DataFrame({
            "country": ["USA", "UK", "France", "Germany", "Japan"],
            "gdp": [21400000, 2940000, 2830000, 3860000, 5150000],
            "happiness": [7.3, 7.2, 6.5, 7.0, 5.9],
            "population": [331000000, 67000000, 67000000, 83000000, 125000000]
        })
        print("✅ 测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试 1: 简单查询
        print("\n🔍 测试 1: 数据查询")
        print("问题: 哪个国家最幸福？")
        try:
            result1 = df.chat("Which country has the highest happiness score?")
            print(f"✅ 查询成功!")
            print(f"结果: {result1}")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
        
        # 测试 2: 数据分析
        print("\n📊 测试 2: 数据分析")
        print("问题: GDP 和幸福指数的相关性")
        try:
            result2 = df.chat("What is the correlation between GDP and happiness?")
            print(f"✅ 分析成功!")
            print(f"结果: {result2}")
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            print("继续下一个测试...")
        
        # 测试 3: 数据统计
        print("\n📈 测试 3: 数据统计")
        print("问题: 平均 GDP 是多少？")
        try:
            result3 = df.chat("What is the average GDP?")
            print(f"✅ 统计成功!")
            print(f"结果: {result3}")
        except Exception as e:
            print(f"❌ 统计失败: {e}")
            print("继续下一个测试...")
        
        # 测试 4: 数据可视化
        print("\n🎨 测试 4: 数据可视化")
        print("问题: 绘制 GDP 柱状图")
        try:
            result4 = df.chat("Create a bar chart showing GDP by country")
            print(f"✅ 可视化成功!")
            print(f"结果: {result4}")
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            print("这可能是由于图表生成的复杂性")
        
        print("\n🎉 PandasAI V3 + Gemini 基本功能验证成功!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False

def test_basic_gemini_connection():
    """测试基本的 Gemini 连接"""
    print("\n🧪 测试基本 Gemini 连接")
    print("-" * 30)
    
    try:
        import google.generativeai as genai
        
        api_key = "AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50"
        genai.configure(api_key=api_key)
        
        # 创建模型实例
        model = genai.GenerativeModel('gemini-pro')
        
        # 简单测试
        response = model.generate_content("Hello, how are you?")
        print(f"✅ Gemini 直接连接成功!")
        print(f"响应: {response.text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Gemini 直接连接失败: {e}")
        return False

def main():
    print("🚀 PandasAI V3 + Google Gemini 完整验证")
    print("=" * 60)
    
    # 测试基本 Gemini 连接
    gemini_basic = test_basic_gemini_connection()
    
    # 测试 PandasAI V3 集成
    pandasai_ok = test_pandasai_v3_with_gemini()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  Gemini 基本连接: {'✅ 通过' if gemini_basic else '❌ 失败'}")
    print(f"  PandasAI V3 集成: {'✅ 通过' if pandasai_ok else '❌ 失败'}")
    
    if gemini_basic and pandasai_ok:
        print("\n🎉 所有测试通过! PandasAI V3 + Gemini 配置成功!")
        print("\n📚 您现在可以使用以下方式进行数据分析:")
        print("1. 创建 DataFrame: df = pai.DataFrame(your_data)")
        print("2. 配置 LLM: pai.config.set({'llm': LiteLLM(model='gemini/gemini-pro')})")
        print("3. 开始对话: df.chat('your question')")
    elif gemini_basic:
        print("\n⚠️  Gemini API 正常，但 PandasAI 集成有问题")
        print("请检查 pandasai-litellm 是否正确安装")
    else:
        print("\n❌ 测试失败，请检查:")
        print("1. API 密钥是否正确")
        print("2. 网络连接是否正常")
        print("3. 是否有足够的 API 配额")
    
    return gemini_basic and pandasai_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
