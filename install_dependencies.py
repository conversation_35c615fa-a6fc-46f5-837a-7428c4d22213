"""
PandasAI V3 依赖安装脚本
自动安装所需的依赖包
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package):
    """安装单个包"""
    print(f"📦 安装 {package}...")
    success, stdout, stderr = run_command(f"pip install {package}")
    
    if success:
        print(f"✅ {package} 安装成功")
        return True
    else:
        print(f"❌ {package} 安装失败: {stderr}")
        return False

def main():
    """主安装流程"""
    print("🚀 开始安装 PandasAI V3 依赖包")
    print("=" * 50)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️  未检测到虚拟环境，建议在虚拟环境中安装")
        response = input("是否继续安装? (y/n): ")
        if response.lower() != 'y':
            print("安装已取消")
            return False
    
    # 需要安装的包列表
    packages = [
        "pandas>=1.5.0",
        "pandasai",
        "openai",
        "google-generativeai",  # 用于 Google Gemini
        "matplotlib",  # 用于图表生成
        "seaborn",     # 用于更好的图表样式
    ]
    
    # 安装包
    failed_packages = []
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📋 安装结果总结:")
    
    if not failed_packages:
        print("🎉 所有依赖包安装成功!")
        print("\n下一步:")
        print("1. 设置 API 密钥环境变量")
        print("2. 运行验证脚本: python pandasai_verification.py")
        return True
    else:
        print(f"❌ {len(failed_packages)} 个包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装失败的包或检查网络连接")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
