# PandasAI API 密钥配置文件
# 复制此文件为 .env 并填入您的真实 API 密钥

# 阿里通义千问 API 密钥 (推荐 - 中国大陆用户)
# 获取地址: https://dashscope.console.aliyun.com/
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# OpenAI API 密钥 (国际用户)
# 获取地址: https://platform.openai.com/api-keys
# OPENAI_API_KEY=sk-your_openai_api_key_here

# Google Gemini API 密钥 (备选)
# 获取地址: https://makersuite.google.com/app/apikey
# GOOGLE_API_KEY=your_google_api_key_here

# 使用说明:
# 1. 将此文件重命名为 .env
# 2. 将 your_dashscope_api_key_here 替换为您的真实 Dashscope API 密钥
# 3. 确保 .env 文件不被提交到版本控制系统

# 推荐中国大陆用户使用阿里通义千问:
# - 访问稳定无障碍
# - 成本比 OpenAI 低 60-80%
# - 中文理解能力更强
