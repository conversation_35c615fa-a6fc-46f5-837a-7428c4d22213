"""
阿里通义千问 API 直接连通性测试
使用官方 OpenAI 兼容接口，跳过 PandasAI 集成
专门测试中国大陆网络环境下的连接状态
"""

import os
import sys
from pathlib import Path

def load_env_file():
    """从 .env 文件加载环境变量"""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ 未找到 .env 文件")
        return False
    
    print("📁 读取 .env 文件...")
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
                    if key.strip() == 'DASHSCOPE_API_KEY':
                        print(f"✅ 从 .env 文件读取 API 密钥: {value[:10]}...")
        return True
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连通性"""
    print("\n🌐 测试网络连通性...")
    
    import socket
    import time
    
    # 测试阿里云域名解析
    try:
        start_time = time.time()
        socket.gethostbyname('dashscope.aliyuncs.com')
        end_time = time.time()
        print(f"✅ 阿里云域名解析成功 ({end_time - start_time:.2f}s)")
        return True
    except Exception as e:
        print(f"❌ 阿里云域名解析失败: {e}")
        return False

def test_dashscope_api_direct():
    """直接测试阿里通义千问 API"""
    print("\n🚀 阿里通义千问 API 直接测试")
    print("=" * 50)
    
    # 检查 API 密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY 环境变量")
        return False
    
    print(f"✅ API 密钥已加载: {api_key[:10]}...")
    
    try:
        # 导入 OpenAI 库
        from openai import OpenAI
        print("✅ OpenAI 库导入成功")
        
        # 创建客户端 - 使用阿里云 Dashscope 的 OpenAI 兼容接口
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        print("✅ Dashscope 客户端创建成功")
        
        # 测试 1: 简单的中文对话
        print("\n🔍 测试 1: 简单中文对话")
        print("问题: 你好，请介绍一下你自己")
        
        try:
            completion = client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {"role": "system", "content": "你是一个有用的AI助手。"},
                    {"role": "user", "content": "你好，请介绍一下你自己"}
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            response_content = completion.choices[0].message.content
            print(f"✅ 测试 1 成功!")
            print(f"通义千问回答: {response_content}")
            
        except Exception as e:
            print(f"❌ 测试 1 失败: {e}")
            return False
        
        # 测试 2: 数据分析相关问题
        print("\n📊 测试 2: 数据分析问题")
        print("问题: 如何计算一组数据的平均值？")
        
        try:
            completion = client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {"role": "system", "content": "你是一个数据分析专家。"},
                    {"role": "user", "content": "如何计算一组数据的平均值？请用Python代码示例说明。"}
                ],
                max_tokens=150,
                temperature=0.3
            )
            
            response_content = completion.choices[0].message.content
            print(f"✅ 测试 2 成功!")
            print(f"通义千问回答: {response_content}")
            
        except Exception as e:
            print(f"❌ 测试 2 失败: {e}")
            return False
        
        # 测试 3: 流式输出
        print("\n🌊 测试 3: 流式输出")
        print("问题: 请简单解释什么是人工智能")
        
        try:
            stream = client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {"role": "user", "content": "请简单解释什么是人工智能"}
                ],
                max_tokens=80,
                temperature=0.5,
                stream=True
            )
            
            print("✅ 流式输出开始:")
            print("通义千问回答: ", end="", flush=True)
            
            full_response = ""
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
            
            print()  # 换行
            print(f"✅ 测试 3 成功! 流式输出完成")
            
        except Exception as e:
            print(f"❌ 测试 3 失败: {e}")
            return False
        
        print("\n🎉 所有 API 测试通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install openai")
        return False
    except Exception as e:
        print(f"❌ API 测试失败: {e}")
        return False

def test_model_availability():
    """测试可用模型列表"""
    print("\n📋 测试可用模型")
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        return False
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        # 测试不同模型
        models_to_test = [
            ("qwen-plus", "通义千问Plus"),
            ("qwen-turbo", "通义千问Turbo"),
            ("qwen-max", "通义千问Max")
        ]
        
        for model_name, model_desc in models_to_test:
            try:
                completion = client.chat.completions.create(
                    model=model_name,
                    messages=[{"role": "user", "content": "测试"}],
                    max_tokens=10
                )
                print(f"✅ {model_desc} ({model_name}) - 可用")
            except Exception as e:
                print(f"❌ {model_desc} ({model_name}) - 不可用: {str(e)[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 阿里通义千问 API 连通性测试")
    print("=" * 60)
    print("测试环境: 中国大陆网络 (无VPN)")
    print("测试目标: 验证 Dashscope API 直接连接")
    
    # 步骤 1: 加载环境变量
    if not load_env_file():
        print("\n❌ 环境变量加载失败")
        return False
    
    # 步骤 2: 测试网络连通性
    if not test_network_connectivity():
        print("\n❌ 网络连通性测试失败")
        return False
    
    # 步骤 3: 直接测试 API
    api_success = test_dashscope_api_direct()
    
    # 步骤 4: 测试模型可用性
    if api_success:
        test_model_availability()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  环境变量加载: ✅ 成功")
    print(f"  网络连通性: ✅ 正常")
    print(f"  API 连接测试: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if api_success:
        print("\n🎉 阿里通义千问 API 连接正常!")
        print("✅ 可以进行下一步: 集成到 PandasAI V3")
        print("\n📚 下一步操作:")
        print("1. 运行: python test_qwen_pandasai_integration.py")
        print("2. 开始使用 PandasAI + 通义千问进行数据分析")
    else:
        print("\n❌ API 连接失败")
        print("🔧 请检查:")
        print("1. API 密钥是否正确")
        print("2. 阿里云账户状态")
        print("3. 网络连接是否稳定")
    
    return api_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
