"""
测试 Google Gemini API 连接
"""

import os
import pandas as pd

def test_gemini_connection():
    print("🧪 测试 Google Gemini API 连接")
    print("=" * 40)
    
    # 设置 API 密钥
    api_key = "AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50"
    os.environ["GOOGLE_API_KEY"] = api_key
    
    print(f"✅ API 密钥已设置: {api_key[:10]}...")
    
    # 测试 Google Generative AI 导入
    try:
        import google.generativeai as genai
        print("✅ google.generativeai 导入成功")
        
        # 配置 API
        genai.configure(api_key=api_key)
        print("✅ Gemini API 配置成功")
        
        # 测试模型列表
        try:
            models = list(genai.list_models())
            print(f"✅ 找到 {len(models)} 个可用模型")
            
            # 显示前几个模型
            for i, model in enumerate(models[:3]):
                print(f"  {i+1}. {model.name}")
                
        except Exception as e:
            print(f"⚠️  获取模型列表失败: {e}")
            
    except ImportError as e:
        print(f"❌ google.generativeai 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Gemini API 配置失败: {e}")
        return False
    
    return True

def test_pandasai_with_gemini():
    print("\n🔍 测试 PandasAI 与 Gemini 集成")
    print("=" * 40)
    
    try:
        # 导入 PandasAI
        from pandasai import SmartDataframe
        print("✅ PandasAI 导入成功")
        
        # 创建测试数据
        df = pd.DataFrame({
            "country": ["USA", "UK", "France", "Germany"],
            "gdp": [21400000, 2940000, 2830000, 3860000],
            "happiness": [7.3, 7.2, 6.5, 7.0]
        })
        print("✅ 测试数据创建成功")
        
        # 创建 SmartDataframe
        sdf = SmartDataframe(df)
        print("✅ SmartDataframe 创建成功")
        
        # 尝试简单查询
        print("\n🤖 执行查询: 哪个国家最幸福？")
        try:
            result = sdf.chat("Which country has the highest happiness score?")
            print(f"✅ 查询成功!")
            print(f"结果: {result}")
            return True
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            print("这可能是由于 PandasAI V3 的 LLM 配置方式发生了变化")
            return False
            
    except ImportError as e:
        print(f"❌ PandasAI 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ PandasAI 测试失败: {e}")
        return False

def main():
    print("🚀 Google Gemini + PandasAI 验证测试")
    print("=" * 50)
    
    # 测试 Gemini API
    gemini_ok = test_gemini_connection()
    
    if not gemini_ok:
        print("\n❌ Gemini API 测试失败，无法继续")
        return False
    
    # 测试 PandasAI 集成
    pandasai_ok = test_pandasai_with_gemini()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"  Gemini API: {'✅ 通过' if gemini_ok else '❌ 失败'}")
    print(f"  PandasAI 集成: {'✅ 通过' if pandasai_ok else '❌ 失败'}")
    
    if gemini_ok and pandasai_ok:
        print("\n🎉 所有测试通过! 您可以开始使用 PandasAI + Gemini 了!")
    elif gemini_ok:
        print("\n⚠️  Gemini API 正常，但 PandasAI 集成有问题")
        print("这可能是 PandasAI V3 的配置方式变化导致的")
    else:
        print("\n❌ 测试失败，请检查 API 密钥和网络连接")
    
    return gemini_ok and pandasai_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
