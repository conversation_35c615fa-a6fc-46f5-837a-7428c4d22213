"""
简化的通义千问 + PandasAI 测试
专注于验证基本集成功能
"""

import os
import pandas as pd
import pandasai as pai
from pandasai.llm.base import LLM
from openai import OpenAI
from pathlib import Path

class SimpleQwenLLM(LLM):
    """简化的通义千问 LLM 类"""
    
    def __init__(self, api_key=None, model="qwen-plus", **kwargs):
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        self.model = model
        
        if not self.api_key:
            raise ValueError("请设置 DASHSCOPE_API_KEY 环境变量")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        super().__init__(**kwargs)
    
    @property
    def type(self) -> str:
        return "qwen_simple"
    
    def call(self, instruction: str, context=None) -> str:
        """简化的调用方法"""
        try:
            print(f"🔍 通义千问收到指令: {instruction[:100]}...")
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个数据分析助手，请用中文回答。"},
                    {"role": "user", "content": instruction}
                ],
                max_tokens=1000,
                temperature=0.1
            )
            
            result = response.choices[0].message.content
            print(f"✅ 通义千问回答: {result[:100]}...")
            return result
            
        except Exception as e:
            print(f"❌ 通义千问调用失败: {e}")
            raise

def load_env():
    """加载环境变量"""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

def test_basic_integration():
    """测试基本集成功能"""
    print("🚀 简化版 PandasAI + 通义千问 测试")
    print("=" * 50)
    
    # 加载环境变量
    load_env()
    
    # 创建 LLM
    llm = SimpleQwenLLM(model="qwen-plus")
    print("✅ 通义千问 LLM 创建成功")
    
    # 配置 PandasAI
    pai.config.set({
        "llm": llm,
        "verbose": True  # 显示详细日志
    })
    print("✅ PandasAI 配置完成")
    
    # 创建简单数据
    df = pai.DataFrame({
        "name": ["Alice", "Bob", "Charlie"],
        "age": [25, 30, 35],
        "salary": [50000, 60000, 70000]
    })
    print("✅ 测试数据创建成功")
    print(df)
    
    # 测试简单查询
    print("\n🔍 测试简单查询...")
    try:
        result = df.chat("What is the average age?")
        print(f"✅ 查询成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_llm():
    """直接测试 LLM 功能"""
    print("\n🔧 直接测试通义千问 LLM")
    print("-" * 30)
    
    load_env()
    llm = SimpleQwenLLM()
    
    # 直接调用测试
    test_instruction = "请解释什么是数据分析"
    try:
        result = llm.call(test_instruction)
        print(f"✅ 直接调用成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 直接调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 通义千问集成诊断测试")
    print("=" * 60)
    
    # 测试 1: 直接 LLM 调用
    llm_success = test_direct_llm()
    
    # 测试 2: PandasAI 集成
    if llm_success:
        integration_success = test_basic_integration()
    else:
        integration_success = False
    
    # 结果总结
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    print(f"  直接 LLM 调用: {'✅ 成功' if llm_success else '❌ 失败'}")
    print(f"  PandasAI 集成: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    if llm_success and integration_success:
        print("\n🎉 完全成功！通义千问 + PandasAI 集成正常工作")
    elif llm_success:
        print("\n⚠️ 部分成功：通义千问 API 正常，但 PandasAI 集成有问题")
        print("这可能是 PandasAI V3 Beta 版本的内部问题")
    else:
        print("\n❌ 集成失败：请检查 API 密钥和网络连接")
    
    return llm_success and integration_success

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n🔧 故障排除建议:")
        print("1. 确认 API 密钥正确")
        print("2. 检查网络连接")
        print("3. 考虑使用稳定版本的 PandasAI")
        print("4. 或者直接使用通义千问 API 进行数据分析")
