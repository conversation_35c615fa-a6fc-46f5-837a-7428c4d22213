"""
阿里云百炼平台 API 正确连接测试
使用更新后的 API 密钥和正确的接口地址
"""

import os
import sys
from pathlib import Path

def load_env_file():
    """从 .env 文件加载环境变量"""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ 未找到 .env 文件")
        return False
    
    print("📁 读取 .env 文件...")
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
                    if key.strip() == 'DASHSCOPE_API_KEY':
                        print(f"✅ 从 .env 文件读取更新后的 API 密钥: {value[:10]}...")
        return True
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return False

def test_bailian_api_methods():
    """测试多种可能的百炼平台连接方式"""
    print("\n🚀 阿里云百炼平台 API 测试")
    print("=" * 50)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY 环境变量")
        return False
    
    print(f"✅ API 密钥已加载: {api_key[:10]}...")
    
    # 测试方法 1: 百炼平台的 OpenAI 兼容接口
    print("\n🔍 方法 1: 百炼平台 OpenAI 兼容接口")
    success1 = test_bailian_openai_compatible(api_key)
    
    # 测试方法 2: Dashscope 原生接口
    print("\n🔍 方法 2: Dashscope 原生接口")
    success2 = test_dashscope_native(api_key)
    
    # 测试方法 3: 百炼平台直接接口
    print("\n🔍 方法 3: 百炼平台直接接口")
    success3 = test_bailian_direct(api_key)
    
    return success1 or success2 or success3

def test_bailian_openai_compatible(api_key):
    """测试百炼平台的 OpenAI 兼容接口"""
    try:
        from openai import OpenAI
        
        # 百炼平台的 OpenAI 兼容接口
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        print("尝试连接百炼平台...")
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个有用的AI助手。"},
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            max_tokens=100
        )
        
        response = completion.choices[0].message.content
        print(f"✅ 百炼平台 OpenAI 兼容接口连接成功!")
        print(f"回答: {response}")
        return True
        
    except Exception as e:
        print(f"❌ 百炼平台 OpenAI 兼容接口失败: {e}")
        return False

def test_dashscope_native(api_key):
    """测试 Dashscope 原生接口"""
    try:
        import requests
        
        url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-plus",
            "input": {
                "messages": [
                    {"role": "system", "content": "你是一个有用的AI助手。"},
                    {"role": "user", "content": "你好"}
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }
        
        print("尝试 Dashscope 原生接口...")
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'output' in result and 'choices' in result['output']:
                content = result['output']['choices'][0]['message']['content']
                print(f"✅ Dashscope 原生接口连接成功!")
                print(f"回答: {content}")
                return True
            else:
                print(f"❌ 响应格式异常: {result}")
                return False
        else:
            print(f"❌ Dashscope 原生接口失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Dashscope 原生接口异常: {e}")
        return False

def test_bailian_direct(api_key):
    """测试百炼平台直接接口"""
    try:
        import requests
        
        # 尝试百炼平台的直接接口
        url = "https://bailian.console.aliyun.com/api/v1/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-plus",
            "messages": [
                {"role": "user", "content": "你好"}
            ]
        }
        
        print("尝试百炼平台直接接口...")
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 百炼平台直接接口连接成功!")
            print(f"响应: {result}")
            return True
        else:
            print(f"❌ 百炼平台直接接口失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 百炼平台直接接口异常: {e}")
        return False

def test_litellm_integration(api_key):
    """测试 LiteLLM 集成"""
    print("\n🔧 测试 LiteLLM 集成")
    
    try:
        from pandasai_litellm import LiteLLM
        import pandasai as pai
        
        # 设置环境变量
        os.environ["DASHSCOPE_API_KEY"] = api_key
        
        # 尝试不同的模型名称格式
        model_formats = [
            "dashscope/qwen-plus",
            "qwen-plus", 
            "alibaba/qwen-plus",
            "aliyun/qwen-plus"
        ]
        
        for model_format in model_formats:
            try:
                print(f"尝试模型格式: {model_format}")
                llm = LiteLLM(model=model_format)
                pai.config.set({"llm": llm})
                
                # 创建简单测试数据
                df = pai.DataFrame({
                    "name": ["Alice", "Bob", "Charlie"],
                    "age": [25, 30, 35],
                    "salary": [50000, 60000, 70000]
                })
                
                result = df.chat("What is the average age?")
                print(f"✅ 模型格式 {model_format} 成功!")
                print(f"结果: {result}")
                return True
                
            except Exception as e:
                print(f"❌ 模型格式 {model_format} 失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ LiteLLM 集成测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🌟 阿里云百炼平台 API 正确连接测试")
    print("=" * 60)
    print("使用更新后的 API 密钥进行测试")
    
    # 加载环境变量
    if not load_env_file():
        return False
    
    # 测试各种连接方式
    api_success = test_bailian_api_methods()
    
    # 如果基础 API 成功，测试 LiteLLM 集成
    litellm_success = False
    if api_success:
        api_key = os.getenv("DASHSCOPE_API_KEY")
        litellm_success = test_litellm_integration(api_key)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  API 连接测试: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"  LiteLLM 集成: {'✅ 成功' if litellm_success else '❌ 失败'}")
    
    if api_success and litellm_success:
        print("\n🎉 所有测试通过! 可以开始使用 PandasAI + 通义千问了!")
        print("\n📚 下一步:")
        print("1. 运行完整的 PandasAI 集成测试")
        print("2. 开始您的数据分析项目")
    elif api_success:
        print("\n✅ API 连接成功，但 LiteLLM 集成需要调整")
        print("🔧 建议检查模型名称格式或 LiteLLM 配置")
    else:
        print("\n❌ API 连接失败")
        print("🔧 请检查:")
        print("1. API 密钥是否正确")
        print("2. 账户状态和权限")
        print("3. 网络连接")
    
    return api_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
