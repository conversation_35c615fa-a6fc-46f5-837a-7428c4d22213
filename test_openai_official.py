"""
PandasAI V3 + OpenAI 官方扩展验证测试
使用 pandasai-openai 官方扩展
"""

import os
import pandas as pd
import pandasai as pai

def test_openai_official_extension():
    """测试 PandasAI V3 官方 OpenAI 扩展"""
    print("🚀 PandasAI V3 + OpenAI 官方扩展测试")
    print("=" * 50)
    
    # 检查 API 密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 未找到 OPENAI_API_KEY 环境变量")
        print("请先设置: set OPENAI_API_KEY=sk-your-api-key")
        return False
    
    print(f"✅ API 密钥已设置: {api_key[:10]}...")
    
    try:
        # 导入官方 OpenAI 扩展
        from pandasai_openai import OpenAI
        print("✅ 官方 OpenAI 扩展导入成功")
        
        # 配置 OpenAI 模型
        llm = OpenAI(api_token=api_key, model="gpt-3.5-turbo")
        print("✅ OpenAI 模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({"llm": llm})
        print("✅ PandasAI 配置设置成功")
        
        # 创建测试数据
        df = pai.DataFrame({
            "country": ["USA", "UK", "France", "Germany", "Japan"],
            "gdp": [21400000, 2940000, 2830000, 3860000, 5150000],
            "happiness": [7.3, 7.2, 6.5, 7.0, 5.9],
            "population": [331000000, 67000000, 67000000, 83000000, 125000000]
        })
        print("✅ 测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试 1: 简单查询
        print("\n🔍 测试 1: 数据查询")
        print("问题: 哪个国家最幸福？")
        try:
            result1 = df.chat("Which country has the highest happiness score?")
            print(f"✅ 查询成功!")
            print(f"结果: {result1}")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
        
        # 测试 2: 数据分析
        print("\n📊 测试 2: 数据分析")
        print("问题: 平均 GDP 是多少？")
        try:
            result2 = df.chat("What is the average GDP of all countries?")
            print(f"✅ 分析成功!")
            print(f"结果: {result2}")
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            print("继续下一个测试...")
        
        # 测试 3: 数据比较
        print("\n📈 测试 3: 数据比较")
        print("问题: GDP 最高的前3个国家是哪些？")
        try:
            result3 = df.chat("What are the top 3 countries with the highest GDP?")
            print(f"✅ 比较成功!")
            print(f"结果: {result3}")
        except Exception as e:
            print(f"❌ 比较失败: {e}")
            print("继续下一个测试...")
        
        print("\n🎉 PandasAI V3 + OpenAI 官方扩展验证成功!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-openai")
        return False
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False

def main():
    print("🚀 PandasAI V3 + OpenAI 官方扩展验证")
    print("=" * 60)
    
    # 检查 API 密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 未找到 OPENAI_API_KEY 环境变量")
        print("\n请按以下步骤设置 API 密钥:")
        print("1. 获取 OpenAI API 密钥: https://platform.openai.com/api-keys")
        print("2. 设置环境变量:")
        print("   Windows: set OPENAI_API_KEY=sk-your-api-key")
        print("   或运行: python setup_openai_api.py")
        return False
    
    # 执行测试
    success = test_openai_official_extension()
    
    if success:
        print("\n📚 使用指南:")
        print("1. 创建 DataFrame: df = pai.DataFrame(your_data)")
        print("2. 配置 OpenAI: pai.config.set({'llm': OpenAI(api_token='your-key')})")
        print("3. 开始对话: df.chat('your question')")
        print("\n💡 提示:")
        print("- 使用 gpt-4 获得更好结果 (费用更高)")
        print("- 使用 gpt-3.5-turbo 获得更快响应 (费用更低)")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
