"""
PandasAI V3 最简单的功能验证
只包含最基本的功能测试
"""

import os
import pandas as pd

def main():
    print("🚀 PandasAI V3 最简单验证")
    print("=" * 40)
    
    # 检查环境变量
    if not (os.getenv("OPENAI_API_KEY") or os.getenv("GOOGLE_API_KEY")):
        print("❌ 请先设置 API 密钥环境变量:")
        print("  OPENAI_API_KEY 或 GOOGLE_API_KEY")
        return False
    
    try:
        # 导入 PandasAI
        from pandasai import SmartDataframe
        from pandasai.llm import OpenAI, GoogleGemini
        print("✅ PandasAI 导入成功")
        
        # 创建简单数据
        df = pd.DataFrame({
            "country": ["USA", "UK", "France"],
            "gdp": [21400000, 2940000, 2830000],
            "happiness": [7.3, 7.2, 6.5]
        })
        print("✅ 数据创建成功")
        
        # 初始化 LLM
        if os.getenv("OPENAI_API_KEY"):
            llm = OpenAI()
            print("✅ OpenAI 连接成功")
        else:
            llm = GoogleGemini()
            print("✅ Google Gemini 连接成功")
        
        # 创建 SmartDataframe
        sdf = SmartDataframe(df, config={"llm": llm})
        print("✅ SmartDataframe 创建成功")
        
        # 简单查询
        print("\n🔍 执行查询: 最幸福的国家是哪个？")
        result = sdf.chat("Which country has the highest happiness score?")
        print(f"✅ 查询结果: {result}")
        
        print("\n🎉 验证成功! PandasAI V3 工作正常")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请运行: pip install pandasai openai google-generativeai")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
