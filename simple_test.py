"""
PandasAI V3 最简单的功能验证
只包含最基本的功能测试
"""

import os
import pandas as pd

# 尝试加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv 不是必需的

def main():
    print("🚀 PandasAI V3 最简单验证")
    print("=" * 40)

    try:
        # 导入 PandasAI
        from pandasai import SmartDataframe
        print("✅ PandasAI 导入成功")

        # 创建简单数据
        df = pd.DataFrame({
            "country": ["USA", "UK", "France"],
            "gdp": [21400000, 2940000, 2830000],
            "happiness": [7.3, 7.2, 6.5]
        })
        print("✅ 数据创建成功")
        print(f"数据预览:\n{df}")

        # 检查是否有 API 密钥
        has_openai = bool(os.getenv("OPENAI_API_KEY"))
        has_google = bool(os.getenv("GOOGLE_API_KEY"))

        if not (has_openai or has_google):
            print("\n⚠️  未检测到 API 密钥环境变量")
            print("要进行完整测试，请设置以下环境变量之一:")
            print("  set OPENAI_API_KEY=sk-your-key")
            print("  set GOOGLE_API_KEY=your-google-key")
            print("\n但我们可以继续进行基本的导入和数据操作测试...")

        # 尝试创建 SmartDataframe (不使用 LLM)
        try:
            sdf = SmartDataframe(df)
            print("✅ SmartDataframe 创建成功 (无 LLM)")
        except Exception as e:
            print(f"❌ SmartDataframe 创建失败: {e}")
            return False

        # 如果有 API 密钥，尝试 LLM 功能
        if has_openai or has_google:
            print("\n🔍 尝试 LLM 功能...")
            try:
                # 简单查询
                result = sdf.chat("Which country has the highest happiness score?")
                print(f"✅ 查询结果: {result}")
            except Exception as e:
                print(f"⚠️  LLM 查询失败: {e}")
                print("这可能是由于 API 密钥问题或网络连接问题")

        print("\n🎉 基本验证成功! PandasAI V3 核心功能正常")
        return True

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请运行: pip install pandasai")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
