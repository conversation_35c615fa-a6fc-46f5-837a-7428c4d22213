"""
Google Gemini API 密钥配置脚本
帮助用户安全地设置 Google API 密钥
"""

import os
import getpass

def setup_gemini_api():
    print("🔧 Google Gemini API 密钥配置")
    print("=" * 50)
    
    # 检查是否已经设置了 API 密钥
    current_key = os.getenv("AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50")
    if current_key:
        print(f"✅ 检测到已设置的 API 密钥: {current_key[:10]}...")
        choice = input("是否要更新 API 密钥? (y/n): ").lower()
        if choice != 'y':
            print("保持当前设置")
            return True
    
    print("\n📋 获取 Google API 密钥的步骤:")
    print("1. 访问 https://makersuite.google.com/app/apikey")
    print("2. 登录您的 Google 账户")
    print("3. 点击 'Create API Key'")
    print("4. 复制生成的 API 密钥")
    print()
    
    # 安全地输入 API 密钥
    api_key = getpass.getpass("请输入您的 Google API 密钥 (输入时不会显示): ").strip()
    
    if not api_key:
        print("❌ API 密钥不能为空")
        return False
    
    if not api_key.startswith("AI"):
        print("⚠️  警告: Google API 密钥通常以 'AI' 开头")
        choice = input("是否继续? (y/n): ").lower()
        if choice != 'y':
            return False
    
    # 设置环境变量
    os.environ["GOOGLE_API_KEY"] = api_key
    print("✅ API 密钥已设置到当前会话")
    
    # 测试 API 密钥
    print("\n🧪 测试 API 密钥...")
    try:
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        # 尝试列出可用模型
        models = list(genai.list_models())
        if models:
            print("✅ API 密钥验证成功!")
            print(f"可用模型数量: {len(models)}")
            return True
        else:
            print("⚠️  API 密钥可能有问题，未找到可用模型")
            return False
            
    except Exception as e:
        print(f"❌ API 密钥验证失败: {e}")
        return False

def show_permanent_setup_instructions():
    print("\n" + "=" * 50)
    print("🔒 永久设置 API 密钥 (可选)")
    print("=" * 50)
    print("如果您想永久设置 API 密钥，可以选择以下方法之一:")
    print()
    print("方法 1: 系统环境变量 (推荐)")
    print("1. 按 Win + R，输入 'sysdm.cpl'")
    print("2. 点击 '环境变量' 按钮")
    print("3. 在 '用户变量' 中点击 '新建'")
    print("4. 变量名: GOOGLE_API_KEY")
    print("5. 变量值: 您的 API 密钥")
    print("6. 重启 PowerShell/命令提示符")
    print()
    print("方法 2: .env 文件")
    print("1. 在项目根目录创建 .env 文件")
    print("2. 添加一行: GOOGLE_API_KEY=your_api_key_here")
    print("3. 确保 .env 文件不被提交到版本控制")
    print()
    print("方法 3: PowerShell 配置文件")
    print("1. 编辑 PowerShell 配置文件")
    print("2. 添加: $env:GOOGLE_API_KEY='your_api_key_here'")

def main():
    success = setup_gemini_api()
    
    if success:
        print("\n🎉 配置完成!")
        print("现在您可以运行验证脚本:")
        print("  python simple_test.py")
        print("  python pandasai_verification.py")
        
        show_permanent_setup_instructions()
    else:
        print("\n❌ 配置失败，请检查您的 API 密钥")
    
    return success

if __name__ == "__main__":
    main()
