2025-08-03 10:47:53 [INFO] Question: Which country has the highest happiness score?
2025-08-03 10:47:54 [INFO] Running PandasAI with litellm LLM...
2025-08-03 10:47:54 [INFO] Prompt ID: 3fa05947-5839-4962-8181-ce0e60090141
2025-08-03 10:47:54 [INFO] Generating new code...
2025-08-03 10:47:54 [INFO] Using Prompt: <tables>

<table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


</tables>

You are already provided with the following functions that you can call:
<function>
def execute_sql_query(sql_query: str) -> pd.Dataframe
    """This method connects to the database, executes the sql query and returns the dataframe"""
</function>


Update this initial code:
```python
# TODO: import the required dependencies
import pandas as pd

# Write code here

# Declare result var: 
type (possible values "string", "number", "dataframe", "plot"). Examples: { "type": "string", "value": f"The highest salary is {highest_salary}." } or { "type": "number", "value": 125 } or { "type": "dataframe", "value": pd.DataFrame({...}) } or { "type": "plot", "value": "temp_chart.png" }

```



### QUERY
 Which country has the highest happiness score?

At the end, declare "result" variable as a dictionary of type and value.


Generate python code and return full updated code:

### Note: Use only relevant table for query and do aggregation, sorting, joins and grouby through sql query
2025-08-03 10:47:55 [INFO] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50 "HTTP/1.1 404 Not Found"
2025-08-03 10:47:55 [INFO] An error occurred during code generation: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:47:55 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:55 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:55 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:47:55 [INFO] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50 "HTTP/1.1 404 Not Found"
2025-08-03 10:47:55 [INFO] An error occurred during code generation: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:47:55 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:55 [INFO] Retrying Code Generation (1/3)...
2025-08-03 10:47:55 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:55 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:47:56 [INFO] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50 "HTTP/1.1 404 Not Found"
2025-08-03 10:47:56 [INFO] An error occurred during code generation: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:47:56 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:56 [INFO] Retrying Code Generation (2/3)...
2025-08-03 10:47:56 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:56 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:47:56 [INFO] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50 "HTTP/1.1 404 Not Found"
2025-08-03 10:47:56 [INFO] An error occurred during code generation: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:47:56 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:56 [INFO] Retrying Code Generation (3/3)...
2025-08-03 10:47:56 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:56 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:47:56 [INFO] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50 "HTTP/1.1 404 Not Found"
2025-08-03 10:47:56 [INFO] An error occurred during code generation: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:47:56 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1890, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 780, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 762, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 758, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyDJ70lHczFklH1YARQFLYRkTxd2tyaQw50'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2607, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1894, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1315, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-08-03 10:47:56 [INFO] Maximum retry attempts exceeded. Last error: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-08-03 10:50:04 [INFO] Question: Which country has the highest happiness score?
2025-08-03 10:50:06 [INFO] Running PandasAI with litellm LLM...
2025-08-03 10:50:06 [INFO] Prompt ID: be862f8f-bfbe-4074-8ff6-a0e9a1ef9c37
2025-08-03 10:50:06 [INFO] Generating new code...
2025-08-03 10:50:06 [INFO] Using Prompt: <tables>

<table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


</tables>

You are already provided with the following functions that you can call:
<function>
def execute_sql_query(sql_query: str) -> pd.Dataframe
    """This method connects to the database, executes the sql query and returns the dataframe"""
</function>


Update this initial code:
```python
# TODO: import the required dependencies
import pandas as pd

# Write code here

# Declare result var: 
type (possible values "string", "number", "dataframe", "plot"). Examples: { "type": "string", "value": f"The highest salary is {highest_salary}." } or { "type": "number", "value": 125 } or { "type": "dataframe", "value": pd.DataFrame({...}) } or { "type": "plot", "value": "temp_chart.png" }

```



### QUERY
 Which country has the highest happiness score?

At the end, declare "result" variable as a dictionary of type and value.


Generate python code and return full updated code:

### Note: Use only relevant table for query and do aggregation, sorting, joins and grouby through sql query
2025-08-03 10:50:18 [ERROR] Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-08-03 10:50:18 [INFO] An error occurred during code generation: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:50:18 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:18 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:18 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:50:31 [ERROR] Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-08-03 10:50:31 [INFO] An error occurred during code generation: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:50:31 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:31 [INFO] Retrying Code Generation (1/3)...
2025-08-03 10:50:31 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:31 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:50:43 [ERROR] Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-08-03 10:50:43 [INFO] An error occurred during code generation: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:50:43 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:43 [INFO] Retrying Code Generation (2/3)...
2025-08-03 10:50:43 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:43 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:50:55 [ERROR] Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-08-03 10:50:55 [INFO] An error occurred during code generation: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:50:55 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:55 [INFO] Retrying Code Generation (3/3)...
2025-08-03 10:50:55 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:50:55 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.



Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:51:07 [ERROR] Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-08-03 10:51:07 [INFO] An error occurred during code generation: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:51:07 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-08-03 10:51:07 [INFO] Maximum retry attempts exceeded. Last error: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2301, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2277, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2676, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1811, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 266, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 437, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 430, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 114, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 160, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 685, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-08-03 10:52:10 [INFO] Question: Which country has the highest happiness score?
2025-08-03 10:52:11 [INFO] Running PandasAI with litellm LLM...
2025-08-03 10:52:11 [INFO] Prompt ID: 814cf31c-7b2e-4806-93e4-65c6b0174035
2025-08-03 10:52:11 [INFO] Generating new code...
2025-08-03 10:52:11 [INFO] Using Prompt: <tables>

<table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


</tables>

You are already provided with the following functions that you can call:
<function>
def execute_sql_query(sql_query: str) -> pd.Dataframe
    """This method connects to the database, executes the sql query and returns the dataframe"""
</function>


Update this initial code:
```python
# TODO: import the required dependencies
import pandas as pd

# Write code here

# Declare result var: 
type (possible values "string", "number", "dataframe", "plot"). Examples: { "type": "string", "value": f"The highest salary is {highest_salary}." } or { "type": "number", "value": 125 } or { "type": "dataframe", "value": pd.DataFrame({...}) } or { "type": "plot", "value": "temp_chart.png" }

```



### QUERY
 Which country has the highest happiness score?

At the end, declare "result" variable as a dictionary of type and value.


Generate python code and return full updated code:

### Note: Use only relevant table for query and do aggregation, sorting, joins and grouby through sql query
2025-08-03 10:52:11 [INFO] An error occurred during code generation: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 10:52:11 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:52:11 [INFO] An error occurred during code generation: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 10:52:11 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Retrying Code Generation (1/3)...
2025-08-03 10:52:11 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:52:11 [INFO] An error occurred during code generation: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 10:52:11 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Retrying Code Generation (2/3)...
2025-08-03 10:52:11 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:52:11 [INFO] An error occurred during code generation: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 10:52:11 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Retrying Code Generation (3/3)...
2025-08-03 10:52:11 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="3x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 10:52:11 [INFO] An error occurred during code generation: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 10:52:11 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_litellm\litellm.py", line 68, in call
    completion(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1330, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1205, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3428, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1098, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 391, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 368, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-08-03 10:52:11 [INFO] Maximum retry attempts exceeded. Last error: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=google/gemini-pro
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-08-03 11:12:46 [INFO] Question: Which country has the highest happiness score?
2025-08-03 11:12:47 [INFO] Running PandasAI with openai LLM...
2025-08-03 11:12:47 [INFO] Prompt ID: 735e315a-ccbf-4ffc-9ce0-afdfb064e546
2025-08-03 11:12:47 [INFO] Generating new code...
2025-08-03 11:12:47 [INFO] Using Prompt: <tables>

<table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="4x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

Germany,3860000,7.0

</table>


</tables>

You are already provided with the following functions that you can call:
<function>
def execute_sql_query(sql_query: str) -> pd.Dataframe
    """This method connects to the database, executes the sql query and returns the dataframe"""
</function>


Update this initial code:
```python
# TODO: import the required dependencies
import pandas as pd

# Write code here

# Declare result var: 
type (possible values "string", "number", "dataframe", "plot"). Examples: { "type": "string", "value": f"The highest salary is {highest_salary}." } or { "type": "number", "value": 125 } or { "type": "dataframe", "value": pd.DataFrame({...}) } or { "type": "plot", "value": "temp_chart.png" }

```



### QUERY
 Which country has the highest happiness score?

At the end, declare "result" variable as a dictionary of type and value.


Generate python code and return full updated code:

### Note: Use only relevant table for query and do aggregation, sorting, joins and grouby through sql query
2025-08-03 11:12:48 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:48 [INFO] Retrying request to /chat/completions in 0.390888 seconds
2025-08-03 11:12:49 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:49 [INFO] Retrying request to /chat/completions in 0.866715 seconds
2025-08-03 11:12:50 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:50 [INFO] An error occurred during code generation: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-03 11:12:50 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:50 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:50 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="4x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

Germany,3860000,7.0

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 11:12:51 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:51 [INFO] Retrying request to /chat/completions in 0.451058 seconds
2025-08-03 11:12:52 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:52 [INFO] Retrying request to /chat/completions in 0.928276 seconds
2025-08-03 11:12:53 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:53 [INFO] An error occurred during code generation: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-03 11:12:53 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:53 [INFO] Retrying Code Generation (1/3)...
2025-08-03 11:12:53 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:53 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="4x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

Germany,3860000,7.0

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 11:12:54 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:54 [INFO] Retrying request to /chat/completions in 0.389657 seconds
2025-08-03 11:12:55 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:55 [INFO] Retrying request to /chat/completions in 0.826437 seconds
2025-08-03 11:12:56 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:56 [INFO] An error occurred during code generation: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-03 11:12:56 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:56 [INFO] Retrying Code Generation (2/3)...
2025-08-03 11:12:56 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:56 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="4x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

Germany,3860000,7.0

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 11:12:56 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:56 [INFO] Retrying request to /chat/completions in 0.375535 seconds
2025-08-03 11:12:57 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:57 [INFO] Retrying request to /chat/completions in 0.831582 seconds
2025-08-03 11:12:58 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:58 [INFO] An error occurred during code generation: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-03 11:12:58 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:58 [INFO] Retrying Code Generation (3/3)...
2025-08-03 11:12:58 [INFO] Execution failed with error: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:12:58 [INFO] Using Prompt: <table dialect="duckdb" table_name="table_dda3bf36590ac20a9a6d18a8692ce0ab" columns="[{"name": "country", "type": "string", "description": null, "expression": null, "alias": null}, {"name": "gdp", "type": "integer", "description": null, "expression": null, "alias": null}, {"name": "happiness", "type": "float", "description": null, "expression": null, "alias": null}]" dimensions="4x3">
country,gdp,happiness

USA,21400000,7.3

UK,2940000,7.2

France,2830000,6.5

Germany,3860000,7.0

</table>


The user asked the following question:
### QUERY
 Which country has the highest happiness score?

You generated the following Python code:
None

However, it resulted in the following error:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 165, in generate_code_with_retries
    return self.generate_code(query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\agent\base.py", line 110, in generate_code
    code = self._code_generator.generate_code(prompt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 47, in generate_code
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}


Fix the python code above and return the new python code but the code generated should use execute_sql_query function
2025-08-03 11:12:58 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:58 [INFO] Retrying request to /chat/completions in 0.435643 seconds
2025-08-03 11:12:59 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:12:59 [INFO] Retrying request to /chat/completions in 0.962806 seconds
2025-08-03 11:13:00 [INFO] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-08-03 11:13:00 [INFO] An error occurred during code generation: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-03 11:13:00 [INFO] Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\core\code_generation\base.py", line 34, in generate_code
    code = self._context.config.llm.generate_code(prompt, self._context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai\llm\base.py", line 172, in generate_code
    response = self.call(instruction, context)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 188, in call
    self.chat_completion(self.last_prompt, memory)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandasai_openai\base.py", line 165, in chat_completion
    response = self.client.create(**params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1131, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1256, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1044, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-08-03 11:13:00 [INFO] Maximum retry attempts exceeded. Last error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
