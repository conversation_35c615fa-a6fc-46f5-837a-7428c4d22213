# PandasAI V3 中国大陆地区可用模型分析报告

## 📋 概述

基于对 PandasAI V3 官方 GitHub 仓库 (https://github.com/sinaptik-ai/pandas-ai) 的深入分析，本报告详细介绍了除 OpenAI 外，PandasAI V3 天然集成的模型，特别关注中国大陆地区可用的模型。

## 🏗️ PandasAI V3 架构

PandasAI V3 采用扩展架构，通过以下方式支持不同的 LLM：

### 1. 官方扩展包
- `pandasai-openai` - OpenAI 官方扩展
- `pandasai-litellm` - LiteLLM 统一接口扩展
- `pandasai-docker` - Docker 沙箱扩展

### 2. 配置方式
```python
import pandasai as pai
from pandasai_litellm import LiteLLM

llm = LiteLLM(model="provider/model-name")
pai.config.set({"llm": llm})
```

## 🌏 中国大陆地区可用模型

### ✅ **直接支持的中国模型**

#### 1. **阿里通义千问 (Dashscope)**
- **提供商**: 阿里云
- **官网**: https://dashscope.console.aliyun.com/
- **PandasAI 支持**: ✅ 通过 LiteLLM 直接支持
- **配置方式**:
```python
from pandasai_litellm import LiteLLM
import os

os.environ['DASHSCOPE_API_KEY'] = 'your-api-key'
llm = LiteLLM(model="dashscope/qwen-turbo")
pai.config.set({"llm": llm})
```

**支持的模型**:
| 模型名称 | 调用方式 | 特点 |
|---------|---------|------|
| qwen-turbo | `dashscope/qwen-turbo` | 快速响应，成本低 |
| qwen-plus | `dashscope/qwen-plus` | 平衡性能和成本 |
| qwen-max | `dashscope/qwen-max` | 最高性能 |
| qwen-vl-plus | `dashscope/qwen-vl-plus` | 支持视觉理解 |
| qwen-vl-max | `dashscope/qwen-vl-max` | 最强视觉模型 |
| qwq-32b | `dashscope/qwq-32b` | 推理专用模型 |

#### 2. **DeepSeek**
- **提供商**: DeepSeek
- **官网**: https://deepseek.com/
- **PandasAI 支持**: ✅ 通过 LiteLLM 直接支持
- **配置方式**:
```python
llm = LiteLLM(model="deepseek/deepseek-chat")
```

#### 3. **Moonshot AI (月之暗面)**
- **提供商**: Moonshot AI
- **PandasAI 支持**: ✅ 通过 LiteLLM 直接支持
- **配置方式**:
```python
llm = LiteLLM(model="moonshot/moonshot-v1-8k")
```

### 🔄 **通过 OpenAI 兼容接口支持的模型**

#### 1. **智谱 ChatGLM**
- **提供商**: 智谱AI
- **支持方式**: OpenAI 兼容接口
- **配置方式**:
```python
llm = LiteLLM(
    model="openai/chatglm-6b",
    api_base="https://open.bigmodel.cn/api/paas/v4/",
    api_key="your-zhipu-api-key"
)
```

#### 2. **百度文心一言**
- **提供商**: 百度
- **支持方式**: 通过第三方适配器或 OpenAI 兼容接口
- **状态**: 需要额外配置

#### 3. **腾讯混元**
- **提供商**: 腾讯云
- **支持方式**: 通过 OpenAI 兼容接口
- **状态**: 需要额外配置

### 🌐 **国际模型（中国大陆可用）**

#### 1. **Google Gemini**
- **配置方式**:
```python
llm = LiteLLM(model="gemini/gemini-pro")
# 需要设置 GOOGLE_API_KEY
```

#### 2. **Anthropic Claude**
- **配置方式**:
```python
llm = LiteLLM(model="anthropic/claude-3-sonnet-20240229")
```

#### 3. **Mistral AI**
- **配置方式**:
```python
llm = LiteLLM(model="mistral/mistral-large-latest")
```

## 🛠️ 本地部署模型支持

### 1. **Ollama**
- **支持**: ✅ 完全支持
- **配置方式**:
```python
llm = LiteLLM(model="ollama/llama2")
```
- **优势**: 完全本地化，数据安全

### 2. **VLLM**
- **支持**: ✅ 完全支持
- **适用**: 高性能本地部署

### 3. **LM Studio**
- **支持**: ✅ 完全支持
- **适用**: 本地开发和测试

## 💰 成本对比分析

### 中国模型成本优势
| 提供商 | 模型 | 相对成本 | 性能 |
|--------|------|----------|------|
| 阿里通义千问 | qwen-turbo | 很低 | 中等 |
| 阿里通义千问 | qwen-plus | 低 | 高 |
| DeepSeek | deepseek-chat | 很低 | 高 |
| Moonshot | moonshot-v1 | 中等 | 高 |

### 与国际模型对比
- **成本优势**: 中国模型普遍比 OpenAI GPT-4 便宜 60-80%
- **性能**: 在中文理解方面通常更优秀
- **可用性**: 在中国大陆地区访问更稳定

## 🚀 推荐配置方案

### 方案 1: 阿里通义千问 (推荐)
```python
import os
import pandasai as pai
from pandasai_litellm import LiteLLM

os.environ['DASHSCOPE_API_KEY'] = 'your-dashscope-api-key'
llm = LiteLLM(model="dashscope/qwen-plus")
pai.config.set({"llm": llm})
```

**优势**:
- ✅ 官方直接支持
- ✅ 成本低廉
- ✅ 中文理解优秀
- ✅ 访问稳定

### 方案 2: DeepSeek (高性价比)
```python
llm = LiteLLM(model="deepseek/deepseek-chat")
```

### 方案 3: 本地 Ollama (数据安全)
```python
llm = LiteLLM(model="ollama/qwen:7b")
```

## 📊 功能支持对比

| 功能 | 通义千问 | DeepSeek | Moonshot | OpenAI |
|------|----------|----------|----------|--------|
| 文本生成 | ✅ | ✅ | ✅ | ✅ |
| 代码生成 | ✅ | ✅ | ✅ | ✅ |
| 数据分析 | ✅ | ✅ | ✅ | ✅ |
| 图表生成 | ✅ | ✅ | ✅ | ✅ |
| 中文理解 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 流式输出 | ✅ | ✅ | ✅ | ✅ |

## 🔧 实际配置示例

### 完整的通义千问配置
```python
import os
import pandasai as pai
from pandasai_litellm import LiteLLM

# 设置 API 密钥
os.environ['DASHSCOPE_API_KEY'] = 'your-api-key'

# 配置模型
llm = LiteLLM(
    model="dashscope/qwen-plus",
    temperature=0.1,  # 控制随机性
    max_tokens=2000   # 最大输出长度
)

# 设置 PandasAI 配置
pai.config.set({
    "llm": llm,
    "save_charts": True,
    "save_charts_path": "./charts/"
})

# 创建数据并测试
df = pai.DataFrame({
    "城市": ["北京", "上海", "广州", "深圳"],
    "GDP": [40000, 43000, 28000, 32000],
    "人口": [2154, 2489, 1868, 1756]
})

result = df.chat("哪个城市的人均GDP最高？")
print(result)
```

## 📝 总结

PandasAI V3 通过 LiteLLM 扩展为中国大陆用户提供了丰富的模型选择：

### ✅ **直接支持**
- 阿里通义千问 (推荐)
- DeepSeek
- Moonshot AI

### 🔄 **间接支持**
- 智谱 ChatGLM
- 百度文心一言
- 腾讯混元

### 🏠 **本地部署**
- Ollama
- VLLM
- LM Studio

**建议**: 对于中国大陆用户，推荐使用阿里通义千问作为首选，DeepSeek 作为备选方案。
