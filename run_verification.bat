@echo off
echo ========================================
echo PandasAI V3 功能验证自动化脚本
echo ========================================

echo.
echo 步骤 1: 检查 Python 环境
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请先安装 Python
    pause
    exit /b 1
)

echo.
echo 步骤 2: 安装依赖包
echo 正在安装 PandasAI 及相关依赖...
python install_dependencies.py
if %errorlevel% neq 0 (
    echo 警告: 依赖安装可能有问题，但继续执行验证
)

echo.
echo 步骤 3: 检查 API 密钥
if not defined OPENAI_API_KEY if not defined GOOGLE_API_KEY (
    echo.
    echo ========================================
    echo 重要: 需要设置 API 密钥
    echo ========================================
    echo 请选择以下选项之一:
    echo.
    echo 选项 1 - OpenAI (推荐):
    echo   set OPENAI_API_KEY=sk-your-api-key
    echo.
    echo 选项 2 - Google Gemini (免费):
    echo   set GOOGLE_API_KEY=your-google-api-key
    echo.
    echo 设置完成后重新运行此脚本
    pause
    exit /b 1
)

echo ✅ 检测到 API 密钥配置

echo.
echo 步骤 4: 运行简单验证
echo 正在运行简单功能验证...
python simple_test.py
if %errorlevel% neq 0 (
    echo 简单验证失败，跳过完整验证
    pause
    exit /b 1
)

echo.
echo 步骤 5: 运行完整验证
echo 正在运行完整功能验证...
python pandasai_verification.py

echo.
echo ========================================
echo 验证完成!
echo ========================================
pause
