"""
阿里通义千问 + PandasAI V3 集成测试
在 API 密钥验证成功后运行此脚本
"""

import os
import sys
from pathlib import Path

def load_env_file():
    """从 .env 文件加载环境变量"""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ 未找到 .env 文件")
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        return True
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return False

def test_qwen_pandasai_integration():
    """测试通义千问与 PandasAI V3 的完整集成"""
    print("🚀 阿里通义千问 + PandasAI V3 集成测试")
    print("=" * 60)
    
    # 加载环境变量
    if not load_env_file():
        return False
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到 DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API 密钥已加载: {api_key[:10]}...")
    
    try:
        # 导入必要的库
        import pandasai as pai
        from pandasai_litellm import LiteLLM
        print("✅ PandasAI 和 LiteLLM 导入成功")
        
        # 配置通义千问模型
        llm = LiteLLM(
            model="dashscope/qwen-plus",
            temperature=0.1,
            max_tokens=2000
        )
        print("✅ 通义千问模型配置成功")
        
        # 设置 PandasAI 配置
        pai.config.set({
            "llm": llm,
            "save_charts": True,
            "save_charts_path": "./charts/",
            "verbose": True
        })
        print("✅ PandasAI 配置完成")
        
        # 创建中文测试数据
        df = pai.DataFrame({
            "城市": ["北京", "上海", "广州", "深圳", "杭州", "成都"],
            "GDP": [40000, 43000, 28000, 32000, 18000, 20000],  # 单位：亿元
            "人口": [2154, 2489, 1868, 1756, 1220, 1658],      # 单位：万人
            "面积": [16410, 6340, 7434, 1997, 16596, 14335],   # 单位：平方公里
            "房价": [65000, 70000, 45000, 55000, 35000, 25000] # 单位：元/平米
        })
        print("✅ 中文测试数据创建成功")
        print(f"数据预览:\n{df}")
        
        # 测试 1: 基础数据查询
        print("\n🔍 测试 1: 基础数据查询")
        print("问题: 哪个城市的GDP最高？")
        try:
            result1 = df.chat("哪个城市的GDP最高？")
            print(f"✅ 测试 1 成功!")
            print(f"通义千问回答: {result1}")
        except Exception as e:
            print(f"❌ 测试 1 失败: {e}")
            return False
        
        # 测试 2: 数据计算
        print("\n📊 测试 2: 数据计算")
        print("问题: 计算每个城市的人均GDP")
        try:
            result2 = df.chat("计算每个城市的人均GDP，并按从高到低排序")
            print(f"✅ 测试 2 成功!")
            print(f"通义千问回答: {result2}")
        except Exception as e:
            print(f"❌ 测试 2 失败: {e}")
            print("继续下一个测试...")
        
        # 测试 3: 数据分析
        print("\n📈 测试 3: 数据分析")
        print("问题: 分析城市发展特点")
        try:
            result3 = df.chat("分析这些城市的发展特点，哪些城市人口密度最高？")
            print(f"✅ 测试 3 成功!")
            print(f"通义千问回答: {result3}")
        except Exception as e:
            print(f"❌ 测试 3 失败: {e}")
            print("继续下一个测试...")
        
        # 测试 4: 数据可视化
        print("\n🎨 测试 4: 数据可视化")
        print("问题: 创建GDP对比图")
        try:
            result4 = df.chat("创建一个柱状图显示各城市的GDP对比，使用不同颜色")
            print(f"✅ 测试 4 成功!")
            print(f"通义千问回答: {result4}")
        except Exception as e:
            print(f"❌ 测试 4 失败: {e}")
            print("可视化功能可能需要额外配置")
        
        # 测试 5: 复杂分析
        print("\n🧠 测试 5: 复杂分析")
        print("问题: 房价与GDP的关系分析")
        try:
            result5 = df.chat("分析房价与GDP之间的关系，哪些城市房价相对于GDP来说偏高？")
            print(f"✅ 测试 5 成功!")
            print(f"通义千问回答: {result5}")
        except Exception as e:
            print(f"❌ 测试 5 失败: {e}")
        
        print("\n🎉 PandasAI V3 + 通义千问集成测试完成!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def create_usage_example():
    """创建使用示例"""
    example_code = '''
# PandasAI V3 + 通义千问使用示例

import os
import pandasai as pai
from pandasai_litellm import LiteLLM

# 1. 设置 API 密钥
os.environ["DASHSCOPE_API_KEY"] = "your-api-key"

# 2. 配置通义千问模型
llm = LiteLLM(
    model="dashscope/qwen-plus",
    temperature=0.1,
    max_tokens=2000
)

# 3. 设置 PandasAI 配置
pai.config.set({
    "llm": llm,
    "save_charts": True,
    "save_charts_path": "./charts/"
})

# 4. 创建数据
df = pai.DataFrame({
    "产品": ["iPhone", "华为", "小米", "OPPO"],
    "销量": [1000, 800, 1200, 600],
    "价格": [6999, 4999, 3999, 3499]
})

# 5. 开始对话分析
result = df.chat("哪个产品性价比最高？")
print(result)

# 6. 生成图表
chart = df.chat("创建销量对比柱状图")
print(chart)
'''
    
    with open("usage_example.py", "w", encoding="utf-8") as f:
        f.write(example_code)
    
    print("📝 使用示例已保存到 usage_example.py")

def main():
    """主函数"""
    print("🌟 PandasAI V3 + 阿里通义千问集成验证")
    print("=" * 60)
    
    # 首先检查是否已通过 API 连通性测试
    print("⚠️  请确保已通过 API 连通性测试")
    print("如未通过，请先运行: python test_dashscope_api_direct.py")
    print()
    
    choice = input("是否继续集成测试? (y/n): ").lower()
    if choice != 'y':
        print("测试已取消")
        return False
    
    # 执行集成测试
    success = test_qwen_pandasai_integration()
    
    # 创建使用示例
    if success:
        create_usage_example()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 集成测试结果:")
    print(f"  PandasAI V3 + 通义千问: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 恭喜! 您现在可以使用中文AI进行数据分析了!")
        print("\n📚 使用指南:")
        print("1. 查看 usage_example.py 了解基本用法")
        print("2. 使用中文提问进行数据分析")
        print("3. 享受更低成本和更好的中文理解能力")
        print("\n💡 提示:")
        print("- 通义千问在中文理解方面表现优秀")
        print("- 成本比 OpenAI 低 60-80%")
        print("- 支持复杂的数据分析和可视化")
    else:
        print("\n❌ 集成测试失败")
        print("🔧 请检查:")
        print("1. API 密钥是否有效")
        print("2. 网络连接是否稳定")
        print("3. 依赖包是否正确安装")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
