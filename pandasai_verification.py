"""
PandasAI V3 功能验证脚本
基于官方 GitHub 文档的最简单验证方案

此脚本将验证以下功能：
1. 数据查询 - 使用自然语言查询数据
2. 数据可视化 - 生成图表
3. 数据分析 - 执行统计分析

使用前请确保设置环境变量：
export OPENAI_API_KEY="your-api-key"
或者
export GOOGLE_API_KEY="your-api-key"
"""

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI, GoogleGemini

def create_sample_data():
    """创建示例数据集"""
    print("📊 创建示例数据集...")
    
    df = pd.DataFrame({
        "country": [
            "United States", "United Kingdom", "France", "Germany", 
            "Italy", "Spain", "Canada", "Australia", "Japan", "China"
        ],
        "gdp": [
            21400000, 2940000, 2830000, 3860000, 
            2070000, 1390000, 1740000, 1380000, 5150000, 14300000
        ],
        "happiness_index": [
            7.3, 7.2, 6.5, 7.0, 6.2, 6.3, 7.4, 7.3, 5.9, 5.1
        ],
        "population": [
            *********, 67000000, 67000000, 83000000,
            60000000, 47000000, 38000000, 25000000, *********, 1400000000
        ]
    })
    
    print(f"✅ 数据集创建完成，包含 {len(df)} 个国家的数据")
    print("\n数据预览:")
    print(df.head())
    return df

def initialize_llm():
    """初始化 LLM - 优先尝试 OpenAI，然后尝试 Google Gemini"""
    print("\n🤖 初始化 LLM...")
    
    # 尝试 OpenAI
    if os.getenv("OPENAI_API_KEY"):
        try:
            llm = OpenAI()
            print("✅ 成功连接到 OpenAI")
            return llm
        except Exception as e:
            print(f"❌ OpenAI 连接失败: {e}")
    
    # 尝试 Google Gemini
    if os.getenv("GOOGLE_API_KEY"):
        try:
            llm = GoogleGemini()
            print("✅ 成功连接到 Google Gemini")
            return llm
        except Exception as e:
            print(f"❌ Google Gemini 连接失败: {e}")
    
    # 如果都失败了
    print("❌ 无法连接到任何 LLM")
    print("请设置以下环境变量之一:")
    print("  export OPENAI_API_KEY='your-openai-key'")
    print("  export GOOGLE_API_KEY='your-google-key'")
    return None

def test_data_query(sdf):
    """测试数据查询功能"""
    print("\n🔍 测试 1: 数据查询")
    print("问题: 哪5个国家最幸福？")
    
    try:
        response = sdf.chat("Which are the 5 happiest countries?")
        print("✅ 查询成功!")
        print("结果:")
        print(response)
        return True
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return False

def test_data_analysis(sdf):
    """测试数据分析功能"""
    print("\n📈 测试 2: 数据分析")
    print("问题: GDP和幸福指数之间的相关性是什么？")
    
    try:
        response = sdf.chat("What is the correlation between GDP and happiness index?")
        print("✅ 分析成功!")
        print("结果:")
        print(response)
        return True
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_data_visualization(sdf):
    """测试数据可视化功能"""
    print("\n📊 测试 3: 数据可视化")
    print("问题: 绘制GDP的直方图，每个柱子使用不同颜色")
    
    try:
        response = sdf.chat("Plot a bar chart of GDP by country, using different colors for each bar")
        print("✅ 可视化成功!")
        print("图表保存路径:")
        print(response)
        return True
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

def main():
    """主函数 - 执行完整的验证流程"""
    print("🚀 PandasAI V3 功能验证开始")
    print("=" * 50)
    
    # 1. 创建数据
    df = create_sample_data()
    
    # 2. 初始化 LLM
    llm = initialize_llm()
    if llm is None:
        print("\n❌ 验证失败: 无法初始化 LLM")
        return False
    
    # 3. 创建 SmartDataframe
    print("\n🧠 创建 SmartDataframe...")
    try:
        sdf = SmartDataframe(df, config={"llm": llm})
        print("✅ SmartDataframe 创建成功")
    except Exception as e:
        print(f"❌ SmartDataframe 创建失败: {e}")
        return False
    
    # 4. 执行测试
    results = []
    results.append(test_data_query(sdf))
    results.append(test_data_analysis(sdf))
    results.append(test_data_visualization(sdf))
    
    # 5. 总结结果
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    
    test_names = ["数据查询", "数据分析", "数据可视化"]
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! PandasAI V3 功能验证成功!")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
